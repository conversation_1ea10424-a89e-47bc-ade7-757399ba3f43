# Flutter Profile Screen Implementation Plan

## 🎯 Objective
Transform the current Flutter Profile screen to achieve pixel-perfect visual consistency with the React Native implementation while preserving all existing functionality.

## 📋 Current vs Target Analysis

### Current Flutter Implementation
- **Structure**: Tab-based layout with Info/Preferences/Settings tabs
- **Colors**: Modern design system (#6366F1 primary, #06B6D4 secondary)
- **Layout**: Single column with tab navigation
- **Profile Card**: 16px border radius, standard elevation
- **Functionality**: Edit mode, image upload, comprehensive settings

### Target React Native Implementation  
- **Structure**: Scrollable sections without tabs
- **Colors**: Airbnb-style (#FF385C primary, #00A699 secondary)
- **Layout**: Grid-based sections with statistics and achievements
- **Profile Card**: 24px border radius, prominent shadows
- **Functionality**: Quick actions grid, travel statistics, achievements

## 🔄 Implementation Strategy

### Phase 1: Color System Migration (Priority: HIGH)
**Objective**: Replace current design system colors with Airbnb-style palette

**Changes Required**:
```dart
// Current colors → New colors
AppTheme.primaryColor: Color(0xFF6366F1) → Color(0xFFFF385C)
AppTheme.secondaryColor: Color(0xFF06B6D4) → Color(0xFF00A699)
AppTheme.accentColor: Color(0xFF10B981) → Color(0xFFFC642D)
```

**Files to Update**:
- `lib/theme/app_theme.dart` - Add new color constants
- `lib/screens/profile_screen.dart` - Update color references

**Implementation Steps**:
1. Add new Airbnb color constants to AppTheme
2. Create backward compatibility aliases
3. Update profile screen color references
4. Test visual consistency

### Phase 2: Profile Card Redesign (Priority: HIGH)
**Objective**: Match React Native profile card specifications exactly

**Current Structure**:
```dart
Container(
  decoration: BoxDecoration(
    gradient: AppTheme.cardGradient,
    borderRadius: BorderRadius.circular(16), // Current
    boxShadow: AppTheme.shadowSmall,
  ),
)
```

**Target Structure**:
```dart
Container(
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(24), // Increased
    boxShadow: [
      BoxShadow(
        color: Color(0x1F222222),
        offset: Offset(0, 6),
        blurRadius: 16,
        spreadRadius: 0,
      ),
    ],
  ),
  child: Stack(
    children: [
      // Subtle gradient overlay
      Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0x14FF385C), Color(0x1400A699)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(24),
        ),
      ),
      // Profile content
      Padding(
        padding: EdgeInsets.all(32), // Increased from 24
        child: _buildProfileHeader(user),
      ),
    ],
  ),
)
```

**Key Changes**:
- Border radius: 16px → 24px
- Padding: 24px → 32px
- Shadow: Enhanced elevation and blur
- Background: Gradient overlay on white base
- Avatar: 80px with 3px white border
- Camera button: 28px positioned absolute

### Phase 3: Layout Restructure (Priority: MEDIUM)
**Objective**: Replace tab-based layout with scrollable sections

**Current Layout**:
```
Column(
  children: [
    ProfileHeader,
    TabBar(Info, Preferences, Settings),
    Expanded(TabBarView),
  ],
)
```

**Target Layout**:
```
SingleChildScrollView(
  child: Column(
    children: [
      Header,
      ProfileCard,
      QuickActionsGrid,
      TravelStatistics,
      Achievements,
      AccountSettings,
      AppPreferences,
      SupportLegal,
      LogoutButton,
      AppInfo,
    ],
  ),
)
```

**Implementation Steps**:
1. Remove TabController and TabBar widgets
2. Convert tab content to individual sections
3. Add proper spacing between sections (24px)
4. Implement RefreshIndicator for pull-to-refresh

### Phase 4: Quick Actions Grid (Priority: MEDIUM)
**Objective**: Create 2x2 grid of action cards

**Implementation**:
```dart
Widget _buildQuickActionsGrid() {
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 24),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF222222),
          ),
        ),
        SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: _quickActions.map((action) => _buildQuickActionCard(action)).toList(),
        ),
      ],
    ),
  );
}
```

**Quick Actions Data**:
```dart
final List<QuickAction> _quickActions = [
  QuickAction(
    title: 'Edit Profile',
    description: 'Update your personal information',
    icon: Icons.edit,
    backgroundColor: Color(0x26FF385C), // primary + 15% opacity
    onTap: () => setState(() => _isEditing = true),
  ),
  QuickAction(
    title: 'Travel Preferences',
    description: 'Customize your travel style',
    icon: Icons.settings,
    backgroundColor: Color(0x2600A699), // secondary + 15% opacity
    onTap: () => _navigateToPreferences(),
  ),
  // ... more actions
];
```

### Phase 5: Travel Statistics Section (Priority: MEDIUM)
**Objective**: Add travel statistics with progress indicators

**Implementation**:
```dart
Widget _buildTravelStatistics() {
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 24),
    child: Column(
      children: [
        Row(
          children: [
            Icon(Icons.trending_up, color: Color(0xFFFF385C), size: 20),
            SizedBox(width: 8),
            Text('Travel Statistics', style: _sectionTitleStyle),
          ],
        ),
        SizedBox(height: 8),
        Text('Your journey at a glance', style: _sectionSubtitleStyle),
        SizedBox(height: 24),
        GridView.count(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.1,
          crossAxisSpacing: 8,
          mainAxisSpacing: 16,
          children: _travelStats.map((stat) => _buildStatCard(stat)).toList(),
        ),
      ],
    ),
  );
}
```

### Phase 6: Achievement System (Priority: LOW)
**Objective**: Implement achievement cards with gradient backgrounds

**Implementation**:
```dart
Widget _buildAchievementCard(Achievement achievement) {
  return Container(
    margin: EdgeInsets.only(bottom: 16),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(16),
      gradient: achievement.unlocked 
        ? LinearGradient(colors: achievement.gradientColors)
        : LinearGradient(colors: [Color(0x33B0B0B0), Color(0x1AB0B0B0)]),
      boxShadow: [
        BoxShadow(
          color: Color(0x26222222),
          offset: Offset(0, 4),
          blurRadius: 12,
        ),
      ],
    ),
    child: Padding(
      padding: EdgeInsets.all(24),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              color: Colors.white.withOpacity(0.2),
            ),
            child: Icon(
              achievement.icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: achievement.unlocked ? Colors.white : Color(0xFFB0B0B0),
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  achievement.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: achievement.unlocked 
                      ? Colors.white.withOpacity(0.9) 
                      : Color(0xFFB0B0B0),
                  ),
                ),
                if (!achievement.unlocked) ...[
                  SizedBox(height: 8),
                  _buildProgressBar(achievement.progress),
                ],
              ],
            ),
          ),
          if (achievement.unlocked)
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: Colors.white.withOpacity(0.2),
              ),
              child: Icon(Icons.check_circle, color: Colors.white, size: 16),
            ),
        ],
      ),
    ),
  );
}
```

## 🔧 Implementation Timeline

### Week 1: Foundation
- [ ] Update color system in AppTheme
- [ ] Redesign profile card with new specifications
- [ ] Test color consistency across app

### Week 2: Layout Transformation
- [ ] Remove tab-based navigation
- [ ] Implement scrollable section layout
- [ ] Add proper spacing and padding

### Week 3: Feature Implementation
- [ ] Create Quick Actions grid
- [ ] Implement Travel Statistics section
- [ ] Add basic achievement system

### Week 4: Polish & Testing
- [ ] Fine-tune shadows and elevations
- [ ] Test on various screen sizes
- [ ] Ensure functionality preservation
- [ ] Performance optimization

## 🎨 Design System Updates Required

### New Constants to Add
```dart
// Airbnb Color Palette
static const Color airbnbPrimary = Color(0xFFFF385C);
static const Color airbnbSecondary = Color(0xFF00A699);
static const Color airbnbAccent = Color(0xFFFC642D);

// Enhanced Shadows
static List<BoxShadow> get shadowProfile => [
  BoxShadow(
    color: Color(0x1F222222),
    offset: Offset(0, 6),
    blurRadius: 16,
    spreadRadius: 0,
  ),
];

// Typography Weights
static const FontWeight fontWeightSemibold = FontWeight.w600;
```

## ✅ Success Criteria

### Visual Consistency
- [ ] Profile card matches React Native specifications exactly
- [ ] Color scheme matches Airbnb palette
- [ ] Grid layouts match 2x2 specifications
- [ ] Shadows and elevations match target design
- [ ] Typography weights and sizes match exactly

### Functionality Preservation
- [ ] All existing edit functionality works
- [ ] Image upload functionality preserved
- [ ] User data integrity maintained
- [ ] Navigation flows preserved
- [ ] Performance targets maintained (<100MB memory, 60fps)

### Code Quality
- [ ] ≤150 line batch editing maintained
- [ ] Guardrails methodology followed
- [ ] No breaking changes to existing APIs
- [ ] Comprehensive testing completed

This implementation plan provides a systematic approach to achieving pixel-perfect consistency while preserving all existing functionality.
