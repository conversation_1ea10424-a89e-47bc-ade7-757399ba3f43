import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native TopGuideCard replica
/// Horizontal scrolling card for Top Guides section
/// Matches reference design exactly with centered layout and proper styling
class RNTopGuideCard extends StatelessWidget {
  final String name;
  final String specialty;
  final String location; // Added location field
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String experience;
  final bool isCertified; // Added certification status
  final VoidCallback onPressed;
  final VoidCallback? onFavoritePressed;
  final bool isFavorite;

  const RNTopGuideCard({
    super.key,
    required this.name,
    required this.specialty,
    required this.location, // Added location parameter
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.experience,
    this.isCertified = true, // Default to certified
    required this.onPressed,
    this.onFavoritePressed,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 190, // Enhanced width for better visibility
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(
              12), // Flatter border radius to match design system
          boxShadow: const [
            BoxShadow(
              color: AppTheme.shadowColor,
              offset: Offset(0, 4), // Increased offset for better elevation
              blurRadius: 10, // Increased blur for softer shadow
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min, // Prevent overflow
          children: [
            // Profile Image Section
            Padding(
              padding: const EdgeInsets.only(top: 16, bottom: 12),
              child: Container(
                width: 70, // Larger profile image for better visibility
                height: 70,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppTheme.borderLight,
                    width: 1, // Thinner border for cleaner appearance
                  ),
                  boxShadow: const [
                    BoxShadow(
                      color: AppTheme.shadowColor,
                      offset: Offset(0, 2),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: ClipOval(
                  child: imageUrl.isNotEmpty
                      ? Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: AppTheme.backgroundSecondary,
                              child: const Icon(
                                Icons.person,
                                size: 35,
                                color: AppTheme.textSecondaryColor,
                              ),
                            );
                          },
                        )
                      : Container(
                          color: AppTheme.backgroundSecondary,
                          child: const Icon(
                            Icons.person,
                            size: 35,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                ),
              ),
            ),

            // Content Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Guide Name
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: AppTheme.fontWeightBold,
                      color: AppTheme.textPrimaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),

                  // Specialty
                  Text(
                    specialty,
                    style: const TextStyle(
                      fontSize: 13,
                      color: AppTheme.textSecondaryColor,
                      fontWeight: AppTheme.fontWeightMedium,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),

                  // Location with defensive layout
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          location,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Rating with defensive layout
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.star,
                        size: 14,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        rating.toStringAsFixed(1),
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: AppTheme.fontWeightMedium,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          '($reviewCount)',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Experience Badge
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius:
                          BorderRadius.circular(8), // Flatter border radius
                      boxShadow: const [
                        BoxShadow(
                          color: AppTheme.shadowColor,
                          offset: Offset(0, 2),
                          blurRadius: 4,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Text(
                      experience,
                      style: const TextStyle(
                        fontSize: 11,
                        fontWeight: AppTheme.fontWeightMedium,
                        color: AppTheme.white,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
