<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Culture Connect</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>culture_connect</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This app needs access to location to show nearby cultural experiences.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>This app needs access to location to show nearby cultural experiences.</string>
		<key>NSCameraUsageDescription</key>
		<string>CultureConnect uses your camera to provide immersive AR experiences that bring cultural landmarks to life. Camera access is required for AR features and profile photos.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This app needs access to location for AR experiences and to show nearby cultural landmarks.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>This app needs photo library access to select pictures for your profile and
			experiences.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>This app needs microphone access for video recording of experiences.</string>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true />
		</dict>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<false />
		<key>GMSApiKey</key>
		<string>AIzaSyDevelopmentKeyForCultureConnectApp</string>

		<!-- ARKit Configuration -->
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arkit</string>
		</array>
	</dict>
</plist>